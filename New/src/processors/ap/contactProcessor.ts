/**
 * Contact Processor for AP Webhook Events
 *
 * Handles contact creation and update events from AutoPatient webhooks by:
 * 1. Checking for calendar property (skip if present)
 * 2. Looking up existing contact in local database
 * 3. Fetching complete contact details from AutoPatient API
 * 4. Comparing timestamps for recent updates
 * 5. Syncing with CliniCore platform
 * 6. Updating local database with results
 *
 * Performance Critical: Must complete within 20 seconds (Cloudflare Workers timeout)
 */

import { dbSchema, getDb } from "@database";
import type {
	APContactWebhookPayload,
	APWebhookPayload,
	GetAPContactType,
	PostCCPatientType,
} from "@type";
import { eq, or } from "drizzle-orm";
import { contactReq, patientReq } from "@/apiClient";
import { logDebug, logError, logInfo, logProcessingStep } from "@/utils/logger";

/**
 * Process contact creation and update webhooks from AutoPatient
 *
 * Implements the 4-step logic:
 * 1. Calendar property check (already done in handler)
 * 2. Local database lookup
 * 3. Timestamp comparison if contact found
 * 4. CliniCore synchronization
 *
 * @param requestId - Request ID from Hono context for logging correlation
 * @param payload - Validated AP contact webhook payload
 * @returns Promise<void> - Completes processing or throws error
 */
export async function contactSyncProcessor(
	requestId: string,
	payload: APContactWebhookPayload,
): Promise<void> {
	const apContactId = payload.contact_id;
	const email = payload.email?.trim() || null;
	const phone = payload.phone?.trim() || null;

	logInfo(
		requestId,
		`Processing contact sync for AP ID: ${apContactId}, Email: ${email}, Phone: ${phone}`,
	);

	// Step 2: Local Database Lookup
	const db = getDb();
	let existingPatient: typeof dbSchema.patient.$inferSelect | undefined;

	try {
		// Primary lookup by apId
		const apIdResults = await db
			.select()
			.from(dbSchema.patient)
			.where(eq(dbSchema.patient.apId, apContactId))
			.limit(1);

		if (apIdResults.length > 0) {
			existingPatient = apIdResults[0];
			logDebug(requestId, `Found existing patient by AP ID: ${existingPatient.id}`);
		} else {
			// Secondary lookup by email and phone
			if (email || phone) {
				const conditions = [];
				if (email) conditions.push(eq(dbSchema.patient.email, email));
				if (phone) conditions.push(eq(dbSchema.patient.phone, phone));

				const emailPhoneResults = await db
					.select()
					.from(dbSchema.patient)
					.where(or(...conditions))
					.limit(1);

				if (emailPhoneResults.length > 0) {
					existingPatient = emailPhoneResults[0];
					logDebug(requestId, `Found existing patient by email/phone: ${existingPatient.id}`);
				}
			}
		}
	} catch (error) {
		logError(requestId, "Database lookup error", error);
		throw new Error(`Failed to lookup existing patient: ${error}`);
	}

	// Step 3A: If Contact Found in Local Database
	if (existingPatient) {
		logInfo(requestId, `Existing patient found: ${existingPatient.id}`);

		// Fetch complete contact details from AutoPatient API
		let apContactData: GetAPContactType;
		try {
			apContactData = await contactReq.get(apContactId);
			logDebug(requestId, `Fetched AP contact data for: ${apContactId}`);
		} catch (error) {
			logError(requestId, "AutoPatient API fetch error", error);
			throw new Error(`Failed to fetch AP contact data: ${error}`);
		}

		// Compare webhook date_updated with local database apUpdatedAt timestamp
		// Use date_updated from webhook payload if available, otherwise use date_created
		const webhookUpdatedDate = payload.date_updated
			? new Date(payload.date_updated)
			: new Date(payload.date_created);
		const localUpdatedDate = existingPatient.apUpdatedAt;

		if (localUpdatedDate) {
			const timeDiffMs = Math.abs(webhookUpdatedDate.getTime() - localUpdatedDate.getTime());
			const timeDiffMinutes = timeDiffMs / (1000 * 60);

			// If timestamps are within ±1 minute: Skip sync
			if (timeDiffMinutes <= 1) {
				logInfo(
					requestId,
					`Recent update detected (${timeDiffMinutes.toFixed(2)} minutes) - skipping sync`,
				);
				return;
			}

			logInfo(
				requestId,
				`Timestamp difference: ${timeDiffMinutes.toFixed(2)} minutes - proceeding with sync`,
			);
		} else {
			logInfo(requestId, "No local timestamp found - proceeding with sync");
		}

		// Continue to CliniCore sync
		await syncWithCliniCore(requestId, existingPatient.id, apContactData, true);
	} else {
		// Step 3B: If Contact NOT Found in Local Database
		logInfo(requestId, `New contact detected: ${apContactId}`);

		// Fetch complete contact details from AutoPatient API
		let apContactData: GetAPContactType;
		try {
			apContactData = await contactReq.get(apContactId);
			logDebug(requestId, `Fetched AP contact data for new contact: ${apContactId}`);
		} catch (error) {
			logError(requestId, "AutoPatient API fetch error", error);
			throw new Error(`Failed to fetch AP contact data: ${error}`);
		}

		// Add to local database
		let localPatientId: string;
		try {
			const now = new Date();
			const newPatientResults = await db
				.insert(dbSchema.patient)
				.values({
					apId: apContactId,
					email: email,
					phone: phone,
					apData: apContactData,
					apUpdatedAt: now,
				})
				.returning({ id: dbSchema.patient.id });

			localPatientId = newPatientResults[0].id;
			logInfo(requestId, `Created new patient record: ${localPatientId}`);
		} catch (error) {
			logError(requestId, "Database insert error", error);
			throw new Error(`Failed to create local patient record: ${error}`);
		}

		// Proceed to CliniCore synchronization
		await syncWithCliniCore(requestId, localPatientId, apContactData, false);
	}

	logProcessingStep(
		requestId,
		`Contact sync completed for AP ID: ${apContactId}`,
	);
}

/**
 * Sync patient data with CliniCore platform
 *
 * Step 4: CliniCore Synchronization
 * - Search CliniCore for existing patient using email and phone
 * - If patient found: Update existing patient data
 * - If patient NOT found: Create new patient record
 *
 * @param requestId - Request ID for logging
 * @param localPatientId - Local database patient ID
 * @param apContactData - AP contact data
 * @param isUpdate - Whether this is an update (true) or new contact (false)
 * @returns Promise<void>
 */
async function syncWithCliniCore(
	requestId: string,
	localPatientId: string,
	apContactData: GetAPContactType,
	isUpdate: boolean,
): Promise<void> {
	const db = getDb();
	const now = new Date();
	const email = apContactData.email?.trim() || null;
	const phone = apContactData.phone?.trim() || null;

	logInfo(requestId, `Starting CliniCore sync for local patient: ${localPatientId}`);

	// Search CliniCore for existing patient using email and phone
	let existingCCPatient = null;
	try {
		if (email) {
			existingCCPatient = await patientReq.search(email);
			if (existingCCPatient) {
				logDebug(requestId, `Found existing CC patient by email: ${existingCCPatient.id}`);
			}
		}

		if (!existingCCPatient && phone) {
			existingCCPatient = await patientReq.search(phone);
			if (existingCCPatient) {
				logDebug(requestId, `Found existing CC patient by phone: ${existingCCPatient.id}`);
			}
		}
	} catch (error) {
		logError(requestId, "CliniCore search error", error);
		// Don't throw here - we can still create a new patient
		logInfo(requestId, "Proceeding with patient creation due to search error");
	}

	// Convert AP contact data to CC patient format
	const ccPatientData: PostCCPatientType = {
		firstName: apContactData.firstName || null,
		lastName: apContactData.lastName || null,
		email: email,
		phoneMobile: phone, // Map phone to phoneMobile as per user preferences
		dob: apContactData.dateOfBirth || null,
		gender: apContactData.gender || null,
		// Add address if available
		...(apContactData.address1 && {
			addresses: [{
				street: apContactData.address1,
				city: apContactData.city || null,
				state: apContactData.state || null,
				zipCode: apContactData.postalCode || null,
			}],
		}),
	};

	let finalCCPatient;
	try {
		if (existingCCPatient) {
			// Update existing patient data
			logInfo(requestId, `Updating existing CC patient: ${existingCCPatient.id}`);
			finalCCPatient = await patientReq.update(existingCCPatient.id, ccPatientData);
		} else {
			// Create new patient record
			logInfo(requestId, "Creating new CC patient");
			finalCCPatient = await patientReq.create(ccPatientData);
		}

		logInfo(requestId, `CC sync completed. CC ID: ${finalCCPatient.id}`);
	} catch (error) {
		logError(requestId, "CliniCore sync error", error);
		throw new Error(`Failed to sync with CliniCore: ${error}`);
	}

	// Update local database with CC sync results
	try {
		await db
			.update(dbSchema.patient)
			.set({
				ccId: finalCCPatient.id,
				ccData: finalCCPatient,
				ccUpdatedAt: now,
				updatedAt: now,
			})
			.where(eq(dbSchema.patient.id, localPatientId));

		logDebug(requestId, `Updated local patient record with CC data: ${localPatientId}`);
	} catch (error) {
		logError(requestId, "Database update error", error);
		throw new Error(`Failed to update local patient record: ${error}`);
	}
}

/**
 * Validate AP contact webhook payload
 *
 * @param payload - Raw webhook payload
 * @returns Validated payload or throws error
 */
export function validateContactWebhookPayload(
	payload: APWebhookPayload,
): APContactWebhookPayload {
	if (!payload || typeof payload !== "object") {
		throw new Error("Invalid payload: must be an object");
	}

	// Ensure calendar property is not present for contact webhooks
	if (payload.calendar) {
		throw new Error("Calendar property found - this should be handled as appointment webhook");
	}

	if (!payload.contact_id) {
		throw new Error("Missing required field: contact_id");
	}

	if (!payload.date_created) {
		throw new Error("Missing required field: date_created");
	}

	if (!payload.location) {
		throw new Error("Missing required field: location");
	}

	// Return as contact webhook payload (calendar property excluded by type)
	return payload as APContactWebhookPayload;
}
